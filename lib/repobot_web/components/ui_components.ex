defmodule RepobotWeb.UI.Components do
  import RepobotWeb.CoreComponents

  alias Phoenix.LiveView.JS

  use Phoenix.Component
  use Gettext, backend: RepobotWeb.Gettext

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"
  attr :id, :string, default: "flash-group", doc: "the optional id of flash container"

  def flash_group(assigns) do
    ~H"""
    <div id={@id}>
      <.flash kind={:info} title={gettext("Success!")} flash={@flash} />
      <.flash kind={:error} title={gettext("Error!")} flash={@flash} />
      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error")}
        phx-connected={hide("#client-error")}
        hidden
      >
        {gettext("Attempting to reconnect")}
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error")}
        phx-connected={hide("#server-error")}
        hidden
      >
        {gettext("Hang in there while we get back on track")}
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>
    </div>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form>
  """
  attr :for, :any, required: true, doc: "the datastructure for the form"
  attr :as, :any, default: nil, doc: "the server side parameter to collect all input under"

  attr :rest, :global,
    include: ~w(autocomplete name rel action enctype method novalidate target multipart),
    doc: "the arbitrary HTML attributes to apply to the form tag"

  slot :inner_block, required: true
  slot :actions, doc: "the slot for form actions, such as a submit button"

  def simple_form(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest}>
      <div class="mt-10 space-y-8 bg-white">
        {render_slot(@inner_block, f)}
        <div :for={action <- @actions} class="mt-2 flex items-center justify-between gap-6">
          {render_slot(action, f)}
        </div>
      </div>
    </.form>
    """
  end

  @doc """
  Generates a generic error message.
  """
  slot :inner_block, required: true

  def error(assigns) do
    ~H"""
    <p class="mt-3 flex gap-3 text-sm leading-6 text-rose-600 phx-no-feedback:hidden">
      <.icon name="hero-exclamation-circle-mini" class="mt-0.5 h-5 w-5 flex-none" />
      {render_slot(@inner_block)}
    </p>
    """
  end

  @doc """
  Renders a pull request badge.

  ## Examples

      <.pull_request_badge pull_request={pull_request} />

  """
  attr :pull_request, :map, required: true, doc: "The pull request to display"

  def pull_request_badge(assigns) do
    ~H"""
    <a
      href={@pull_request.pull_request_url}
      target="_blank"
      rel="noopener noreferrer"
      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-emerald-700 bg-emerald-100 hover:bg-emerald-200 transition-colors duration-150"
    >
      <.icon name="hero-arrow-up-right" class="w-3 h-3 mr-1" />
      PR #{@pull_request.pull_request_number}
    </a>
    """
  end

  @doc """
  A masked input field that shows only part of the API key.
  Shows first 6 and last 4 characters, with asterisks in between.
  """
  attr :field, Phoenix.HTML.FormField, default: nil, doc: "a form field struct"
  attr :id, :string, default: nil, doc: "the id of the input field"
  attr :name, :string, default: nil, doc: "the name of the input field"
  attr :value, :string, default: nil, doc: "the value of the input field"
  attr :class, :string, default: nil, doc: "additional css classes"
  attr :placeholder, :string, default: nil, doc: "placeholder text"
  attr :rest, :global, doc: "additional HTML attributes"

  def masked_api_key_input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign(:name, field.name)
    |> assign(:value, field.value)
    |> assign(:id, field.id)
    |> masked_api_key_input()
  end

  def masked_api_key_input(assigns) do
    assigns =
      assigns
      |> assign_new(:errors, fn -> [] end)
      |> assign_new(:name, fn -> assigns[:id] end)
      |> assign(:displayed_value, if(assigns.value, do: mask_api_key(assigns.value), else: ""))

    ~H"""
    <div phx-feedback-for={@name}>
      <input
        type="text"
        name={"#{@name}_display"}
        id={"#{@id}_display"}
        value={@displayed_value}
        placeholder={@placeholder}
        class={[
          "mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
          "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400",
          @class
        ]}
        phx-change={JS.dispatch("input_changed", to: "##{@id}_hidden")}
      />
      <input type="hidden" name={@name} id={"#{@id}_hidden"} value={@value} phx-hook="UpdateApiKey" />
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  defp mask_api_key(nil), do: nil
  defp mask_api_key(""), do: ""

  defp mask_api_key(value) when byte_size(value) <= 10,
    do: String.duplicate("*", byte_size(value))

  defp mask_api_key(value) do
    prefix = String.slice(value, 0..5)
    suffix = String.slice(value, -4..-1)
    "#{prefix}#{String.duplicate("*", byte_size(value) - 10)}#{suffix}"
  end

  @doc """
  Renders a GitHub app installation banner.

  ## Examples

      <.github_app_installation_banner github_app_installation_required={@github_app_installation_required} github_app_install_url={@github_app_install_url} />
  """
  attr :github_app_installation_required, :boolean, default: false
  attr :github_app_install_url, :string, default: nil

  def github_app_installation_banner(assigns) do
    ~H"""
    <div
      :if={@github_app_installation_required}
      class="w-full bg-yellow-50 border border-yellow-200 text-yellow-900 px-4 py-3 rounded-lg flex items-center justify-between gap-4 mb-4 shadow relative"
    >
      <div class="flex items-center gap-2">
        <.icon name="hero-exclamation-triangle" class="h-6 w-6 text-yellow-500" />
        <span>
          GitHub App is not installed for this organization. Some features will not work.
          <a
            href={@github_app_install_url <> "/installations/new"}
            class="underline font-semibold text-yellow-800 hover:text-yellow-700 ml-1"
          >
            Install the GitHub App
          </a>
        </span>
      </div>
      <button
        type="button"
        onclick="this.parentElement.style.display='none'"
        class="text-yellow-500 hover:text-yellow-700 focus:outline-none"
      >
        <.icon name="hero-x-mark" class="h-5 w-5" />
      </button>
    </div>
    """
  end
end
