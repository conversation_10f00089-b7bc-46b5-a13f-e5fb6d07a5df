/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../css";
@source "../js";
@source "../../lib/repobot_web";

/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
   The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";

/* daisyUI Tailwind Plugin. You can update this file by fetching the latest version with:
   curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui.js
   Make sure to look at the daisyUI changelog: https://daisyui.com/docs/changelog/ */
@plugin "../vendor/daisyui" {
  themes: false;
}

/* daisyUI theme plugin. You can update this file by fetching the latest version with:
  curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui-theme.js
  We ship with two themes, a light one inspired on Phoenix colors and a dark one inspired
  on Elixir colors. Build your own at: https://daisyui.com/theme-generator/ */
@plugin "../vendor/daisyui-theme" {
  name: "dark";
  default: false;
  prefersdark: true;
  color-scheme: "dark";
  --color-base-100: oklch(30.33% 0.016 252.42);
  --color-base-200: oklch(25.26% 0.014 253.1);
  --color-base-300: oklch(20.15% 0.012 254.09);
  --color-base-content: oklch(97.807% 0.029 256.847);
  --color-primary: oklch(58% 0.233 277.117);
  --color-primary-content: oklch(96% 0.018 272.314);
  --color-secondary: oklch(58% 0.233 277.117);
  --color-secondary-content: oklch(96% 0.018 272.314);
  --color-accent: oklch(60% 0.25 292.717);
  --color-accent-content: oklch(96% 0.016 293.756);
  --color-neutral: oklch(37% 0.044 257.287);
  --color-neutral-content: oklch(98% 0.003 247.858);
  --color-info: oklch(58% 0.158 241.966);
  --color-info-content: oklch(97% 0.013 236.62);
  --color-success: oklch(60% 0.118 184.704);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(58% 0.253 17.585);
  --color-error-content: oklch(96% 0.015 12.422);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

@plugin "../vendor/daisyui-theme" {
  name: "light";
  default: true;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(98% 0 0);
  --color-base-200: oklch(96% 0.001 286.375);
  --color-base-300: oklch(92% 0.004 286.32);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(70% 0.213 47.604);
  --color-primary-content: oklch(98% 0.016 73.684);
  --color-secondary: oklch(55% 0.027 264.364);
  --color-secondary-content: oklch(98% 0.002 247.839);
  --color-accent: oklch(0% 0 0);
  --color-accent-content: oklch(100% 0 0);
  --color-neutral: oklch(44% 0.017 285.786);
  --color-neutral-content: oklch(98% 0 0);
  --color-info: oklch(62% 0.214 259.815);
  --color-info-content: oklch(97% 0.014 254.604);
  --color-success: oklch(70% 0.14 182.503);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(65% 0.241 354.308);
  --color-error-content: oklch(97% 0.014 343.198);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading ([".phx-click-loading&", ".phx-click-loading &"]);
@custom-variant phx-submit-loading ([".phx-submit-loading&", ".phx-submit-loading &"]);
@custom-variant phx-change-loading ([".phx-change-loading&", ".phx-change-loading &"]);

/* Make LiveView wrapper divs transparent for layout */
[data-phx-root-id] {
  display: contents
}

/* This file is for your main application CSS */

@import "../node_modules/diff2html/bundles/css/diff2html.min.css";

/* This file is for your main application CSS */

.glow-border {
  position: relative;
  border-radius: 1rem;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.15) 0%,
      rgba(59, 130, 246, 0.1) 30%,
      rgba(245, 158, 11, 0.1) 70%,
      rgba(251, 146, 60, 0.15) 100%);
  padding: 2px;
  box-shadow:
    -35px 35px 65px -15px rgba(30, 64, 175, 0.25),
    35px -35px 65px -15px rgba(245, 158, 11, 0.25);
}

.glow-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.3) 0%,
      rgba(59, 130, 246, 0.2) 30%,
      rgba(245, 158, 11, 0.2) 70%,
      rgba(251, 146, 60, 0.3) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Drag and drop styles */
.sortable-ghost {
  opacity: 0.5;
  background-color: #f0f9ff !important;
  /* Light blue background */
  border: 2px dashed #93c5fd !important;
  /* Blue dashed border */
}

.sortable-drag {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sortable-chosen {
  background-color: #f3f4f6 !important;
  /* Light gray background */
}

/* Custom styles for drag handle */
[data-drag-handle] {
  cursor: grab;
}

[data-drag-handle]:active {
  cursor: grabbing;
}
